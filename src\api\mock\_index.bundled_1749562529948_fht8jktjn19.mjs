var __getOwnPropNames = Object.getOwnPropertyNames;
var __require = /* @__PURE__ */ ((x) => typeof require !== "undefined" ? require : typeof Proxy !== "undefined" ? new Proxy(x, {
  get: (a, b) => (typeof require !== "undefined" ? require : a)[b]
}) : x)(function(x) {
  if (typeof require !== "undefined") return require.apply(this, arguments);
  throw Error('Dynamic require of "' + x + '" is not supported');
});
var __glob = (map) => (path) => {
  var fn = map[path];
  if (fn) return fn();
  throw new Error("Module not found in bundle: " + path);
};
var __commonJS = (cb, mod) => function __require2() {
  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;
};

// src/api/mock/json/accounts.json
var require_accounts = __commonJS({
  "src/api/mock/json/accounts.json"(exports, module) {
    module.exports = {
      id: 1,
      username: "icyfenix",
      name: "\u5468\u5FD7\u660E",
      avatar: "",
      telephone: "***********",
      email: "<EMAIL>",
      location: "\u5510\u5BB6\u6E7E\u6E2F\u6E7E\u5927\u9053\u79D1\u6280\u4E00\u8DEF3\u53F7\u8FDC\u5149\u8F6F\u4EF6\u80A1\u4EFD\u6709\u9650\u516C\u53F8"
    };
  }
});

// src/api/mock/json/advertisements.json
var require_advertisements = __commonJS({
  "src/api/mock/json/advertisements.json"(exports, module) {
    module.exports = [
      {
        id: "fenix",
        image: "/static/carousel/fenix2.png",
        productId: 8
      },
      {
        id: "ai",
        image: "/static/carousel/ai.png",
        productId: 2
      },
      {
        id: "jvm",
        image: "/static/carousel/jvm3.png",
        productId: 1
      }
    ];
  }
});

// src/api/mock/json/authorization.json
var require_authorization = __commonJS({
  "src/api/mock/json/authorization.json"(exports, module) {
    module.exports = {
      access_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX25hbWUiOiJpY3lmZW5peCIsInNjb3BlIjpbIkFMTCJdLCJleHAiOjE1ODQyNTA3MDQsImF1dGhvcml0aWVzIjpbIlJPTEVfVVNFUiIsIlJPTEVfQURNSU4iXSwianRpIjoiMTNmNGNlMWQtNmY2OC00NzQxLWI5YzYtMzkyNzU1OGQ5NzRlIiwiY2xpZW50X2lkIjoiYm9va3N0b3JlX2Zyb250ZW5kIiwidXNlcm5hbWUiOiJpY3lmZW5peCJ9.82awQU4IcLVXr7w6pxcUCWrcEHKq-LRT7ggPT_ZPhE0",
      token_type: "bearer",
      refresh_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX25hbWUiOiJpY3lmZW5peCIsInNjb3BlIjpbIkFMTCJdLCJhdGkiOiIxM2Y0Y2UxZC02ZjY4LTQ3NDEtYjljNi0zOTI3NTU4ZDk3NGUiLCJleHAiOjE1ODU1MzU5MDQsImF1dGhvcml0aWVzIjpbIlJPTEVfVVNFUiIsIlJPTEVfQURNSU4iXSwianRpIjoiY2IwN2ZjZjEtMjViZS00MDRjLTkwNzctY2U5ZTlhZjFjOWEwIiwiY2xpZW50X2lkIjoiYm9va3N0b3JlX2Zyb250ZW5kIiwidXNlcm5hbWUiOiJpY3lmZW5peCJ9.-gNKkhspN1XfVybmS3Rnz2AYFdteZN4kvdEmC4g-aYk",
      expires_in: 10799,
      scope: "ALL",
      authorities: [
        "ROLE_USER",
        "ROLE_ADMIN"
      ],
      username: "icyfenix",
      jti: "13f4ce1d-6f68-4741-b9c6-3927558d974e"
    };
  }
});

// src/api/mock/json/products.json
var require_products = __commonJS({
  "src/api/mock/json/products.json"(exports, module) {
    module.exports = [
      {
        id: 8,
        title: "\u51E4\u51F0\u67B6\u6784\uFF1A\u6784\u5EFA\u53EF\u9760\u7684\u5927\u578B\u5206\u5E03\u5F0F\u7CFB\u7EDF",
        price: 0,
        rate: 0,
        description: "<p>\u8FD9\u662F\u4E00\u90E8\u4EE5\u201C\u5982\u4F55\u6784\u5EFA\u4E00\u5957\u53EF\u9760\u7684\u5206\u5E03\u5F0F\u5927\u578B\u8F6F\u4EF6\u7CFB\u7EDF\u201D\u4E3A\u53D9\u4E8B\u4E3B\u7EBF\u7684\u5F00\u6E90\u6587\u6863\uFF0C\u662F\u4E00\u5E45\u5E2E\u52A9\u5F00\u53D1\u4EBA\u5458\u6574\u7406\u73B0\u4EE3\u8F6F\u4EF6\u67B6\u6784\u5404\u6761\u5206\u652F\u4E2D\u7E41\u591A\u77E5\u8BC6\u70B9\u7684\u6280\u80FD\u5730\u56FE\u3002\u6587\u7AE0\u300A<a href='https://icyfenix.cn/introduction/about-the-fenix-project.html' target=_blank>\u4EC0\u4E48\u662F\u201C\u51E4\u51F0\u67B6\u6784\u201D</a>\u300B\u8BE6\u7EC6\u9610\u8FF0\u4E86\u8FD9\u90E8\u6587\u6863\u7684\u4E3B\u65E8\u3001\u76EE\u6807\u4E0E\u540D\u5B57\u7684\u6765\u7531\uFF0C\u6587\u7AE0\u300A<a href='https://icyfenix.cn/exploration/guide/quick-start.html' target=_blank>\u5982\u4F55\u5F00\u59CB</a>\u300B\u7B80\u8FF0\u4E86\u6587\u6863\u6BCF\u7AE0\u8BA8\u8BBA\u7684\u4E3B\u8981\u8BDD\u9898\u4E0E\u5185\u5BB9\u8BE6\u7565\u5206\u5E03</p>",
        cover: "/static/cover/fenix.png",
        detail: "/static/desc/fenix.jpg",
        specifications: [
          {
            id: 64,
            item: "ISBN",
            value: "9787111349662"
          },
          {
            id: 69,
            item: "\u88C5\u5E27",
            value: "\u5728\u7EBF"
          },
          {
            id: 66,
            item: "\u9875\u6570",
            value: "409"
          },
          {
            id: 68,
            item: "\u51FA\u7248\u5E74",
            value: "2020-6"
          },
          {
            id: 65,
            item: "\u4E66\u540D",
            value: "\u51E4\u51F0\u67B6\u6784"
          },
          {
            id: 68,
            item: "\u526F\u6807\u9898",
            value: "\u6784\u5EFA\u53EF\u9760\u7684\u5927\u578B\u5206\u5E03\u5F0F\u7CFB\u7EDF"
          },
          {
            id: 63,
            item: "\u4F5C\u8005",
            value: "\u5468\u5FD7\u660E"
          },
          {
            id: 67,
            item: "\u51FA\u7248\u793E",
            value: "\u673A\u68B0\u5DE5\u4E1A\u51FA\u7248\u793E"
          }
        ]
      },
      {
        id: 1,
        title: "\u6DF1\u5165\u7406\u89E3Java\u865A\u62DF\u673A\uFF08\u7B2C3\u7248\uFF09",
        price: 129,
        rate: 9.6,
        description: "<p>\u8FD9\u662F\u4E00\u90E8\u4ECE\u5DE5\u4F5C\u539F\u7406\u548C\u5DE5\u7A0B\u5B9E\u8DF5\u4E24\u4E2A\u7EF4\u5EA6\u6DF1\u5165\u5256\u6790JVM\u7684\u8457\u4F5C\uFF0C\u662F\u8BA1\u7B97\u673A\u9886\u57DF\u516C\u8BA4\u7684\u7ECF\u5178\uFF0C\u7E41\u4F53\u7248\u5728\u53F0\u6E7E\u4E5F\u9887\u53D7\u6B22\u8FCE\u3002</p><p>\u81EA2011\u5E74\u4E0A\u5E02\u4EE5\u6765\uFF0C\u524D\u4E24\u4E2A\u7248\u672C\u7D2F\u8BA1\u5370\u523736\u6B21\uFF0C\u9500\u91CF\u8D85\u8FC730\u4E07\u518C\uFF0C\u4E24\u5BB6\u4E3B\u8981\u7F51\u7EDC\u4E66\u5E97\u7684\u8BC4\u8BBA\u8FD190000\u6761\uFF0C\u5185\u5BB9\u4E0A\u8FD1\u4E4E\u96F6\u5DEE\u8BC4\uFF0C\u662F\u539F\u521B\u8BA1\u7B97\u673A\u56FE\u4E66\u9886\u57DF\u4E0D\u53EF\u903E\u8D8A\u7684\u4E30\u7891\uFF0C\u7B2C3\u7248\u5728\u7B2C2\u7248\u7684\u57FA\u7840\u4E0A\u505A\u4E86\u91CD\u5927\u4FEE\u8BA2\uFF0C\u5185\u5BB9\u66F4\u4E30\u5BCC\u3001\u5B9E\u6218\u6027\u66F4\u5F3A\uFF1A\u6839\u636E\u65B0\u7248JDK\u5BF9\u5185\u5BB9\u8FDB\u884C\u4E86\u5168\u65B9\u4F4D\u7684\u4FEE\u8BA2\u548C\u5347\u7EA7\uFF0C\u56F4\u7ED5\u65B0\u6280\u672F\u548C\u751F\u4EA7\u5B9E\u8DF5\u65B0\u589E\u903E10\u4E07\u5B57\uFF0C\u5305\u542B\u8FD150%\u7684\u5168\u65B0\u5185\u5BB9\uFF0C\u5E76\u5BF9\u7B2C2\u7248\u4E2D\u542B\u7CCA\u3001\u7455\u75B5\u548C\u9519\u8BEF\u5185\u5BB9\u8FDB\u884C\u4E86\u4FEE\u6B63\u3002</p><p>\u5168\u4E66\u4E00\u517113\u7AE0\uFF0C\u5206\u4E3A\u4E94\u5927\u90E8\u5206\uFF1A</p><p>\u7B2C\u4E00\u90E8\u5206\uFF08\u7B2C1\u7AE0\uFF09\u8D70\u8FD1Java</p><p>\u7CFB\u7EDF\u4ECB\u7ECD\u4E86Java\u7684\u6280\u672F\u4F53\u7CFB\u3001\u53D1\u5C55\u5386\u7A0B\u3001\u865A\u62DF\u673A\u5BB6\u65CF\uFF0C\u4EE5\u53CA\u52A8\u624B\u7F16\u8BD1JDK\uFF0C\u4E86\u89E3\u8FD9\u90E8\u5206\u5185\u5BB9\u80FD\u5BF9\u5B66\u4E60JVM\u63D0\u4F9B\u826F\u597D\u7684\u6307\u5F15\u3002</p><p>\u7B2C\u4E8C\u90E8\u5206\uFF08\u7B2C2~5\u7AE0\uFF09\u81EA\u52A8\u5185\u5B58\u7BA1\u7406</p><p>\u8BE6\u7EC6\u8BB2\u89E3\u4E86Java\u7684\u5185\u5B58\u533A\u57DF\u4E0E\u5185\u5B58\u6EA2\u51FA\u3001\u5783\u573E\u6536\u96C6\u5668\u4E0E\u5185\u5B58\u5206\u914D\u7B56\u7565\u3001\u865A\u62DF\u673A\u6027\u80FD\u76D1\u63A7\u4E0E\u6545\u969C\u6392\u9664\u7B49\u4E0E\u81EA\u52A8\u5185\u5B58\u7BA1\u7406\u76F8\u5173\u7684\u5185\u5BB9\uFF0C\u4EE5\u53CA10\u4F59\u4E2A\u7ECF\u5178\u7684\u6027\u80FD\u4F18\u5316\u6848\u4F8B\u548C\u4F18\u5316\u65B9\u6CD5\uFF1B</p><p>\u7B2C\u4E09\u90E8\u5206\uFF08\u7B2C6~9\u7AE0\uFF09\u865A\u62DF\u673A\u6267\u884C\u5B50\u7CFB\u7EDF</p><p>\u6DF1\u5165\u5206\u6790\u4E86\u865A\u62DF\u673A\u6267\u884C\u5B50\u7CFB\u7EDF\uFF0C\u5305\u62EC\u7C7B\u6587\u4EF6\u7ED3\u6784\u3001\u865A\u62DF\u673A\u7C7B\u52A0\u8F7D\u673A\u5236\u3001\u865A\u62DF\u673A\u5B57\u8282\u7801\u6267\u884C\u5F15\u64CE\uFF0C\u4EE5\u53CA\u591A\u4E2A\u7C7B\u52A0\u8F7D\u53CA\u5176\u6267\u884C\u5B50\u7CFB\u7EDF\u7684\u5B9E\u6218\u6848\u4F8B\uFF1B</p><p>\u7B2C\u56DB\u90E8\u5206\uFF08\u7B2C10~11\u7AE0\uFF09\u7A0B\u5E8F\u7F16\u8BD1\u4E0E\u4EE3\u7801\u4F18\u5316</p><p>\u8BE6\u7EC6\u8BB2\u89E3\u4E86\u7A0B\u5E8F\u7684\u524D\u3001\u540E\u7AEF\u7F16\u8BD1\u4E0E\u4F18\u5316\uFF0C\u5305\u62EC\u524D\u7AEF\u7684\u6613\u7528\u6027\u4F18\u5316\u63AA\u65BD\uFF0C\u5982\u6CDB\u578B\u3001\u4E3B\u52A8\u88C5\u7BB1\u62C6\u7BB1\u3001\u6761\u4EF6\u7F16\u8BD1\u7B49\u7684\u5185\u5BB9\u7684\u6DF1\u5165\u5206\u6790\uFF1B\u4EE5\u53CA\u540E\u7AEF\u7684\u6027\u80FD\u4F18\u5316\u63AA\u65BD\uFF0C\u5982\u865A\u62DF\u673A\u7684\u70ED\u70B9\u63A2\u6D4B\u65B9\u6CD5\u3001HotSpot \u7684\u5373\u65F6\u7F16\u8BD1\u5668\u3001\u63D0\u524D\u7F16\u8BD1\u5668\uFF0C\u4EE5\u53CA\u5404\u79CD\u5E38\u89C1\u7684\u7F16\u8BD1\u671F\u4F18\u5316\u6280\u672F\uFF1B</p><p>\u7B2C\u4E94\u90E8\u5206\uFF08\u7B2C12~13\u7AE0\uFF09\u9AD8\u6548\u5E76\u53D1</p><p>\u4E3B\u8981\u8BB2\u89E3\u4E86Java\u5B9E\u73B0\u9AD8\u5E76\u53D1\u7684\u539F\u7406\uFF0C\u5305\u62ECJava\u7684\u5185\u5B58\u6A21\u578B\u3001\u7EBF\u7A0B\u4E0E\u534F\u7A0B\uFF0C\u4EE5\u53CA\u7EBF\u7A0B\u5B89\u5168\u548C\u9501\u4F18\u5316\u3002</p><p>\u5168\u4E66\u4EE5\u5B9E\u6218\u4E3A\u5BFC\u5411\uFF0C\u901A\u8FC7\u5927\u91CF\u4E0E\u5B9E\u9645\u751F\u4EA7\u73AF\u5883\u76F8\u7ED3\u5408\u7684\u6848\u4F8B\u5206\u6790\u548C\u5C55\u793A\u4E86\u89E3\u51B3\u5404\u79CDJava\u6280\u672F\u96BE\u9898\u7684\u65B9\u6848\u548C\u6280\u5DE7\u3002</p>",
        cover: "/static/cover/jvm3.jpg",
        detail: "/static/desc/jvm3.jpg",
        specifications: [
          {
            id: 9,
            item: "\u88C5\u5E27",
            value: "\u5E73\u88C5"
          },
          {
            id: 7,
            item: "\u51FA\u7248\u793E",
            value: "\u673A\u68B0\u5DE5\u4E1A\u51FA\u7248\u793E"
          },
          {
            id: 2,
            item: "\u526F\u6807\u9898",
            value: "JVM\u9AD8\u7EA7\u7279\u6027\u4E0E\u6700\u4F73\u5B9E\u8DF5"
          },
          {
            id: 3,
            item: "ISBN",
            value: "9787111641247"
          },
          {
            id: 4,
            item: "\u4E66\u540D",
            value: "\u6DF1\u5165\u7406\u89E3Java\u865A\u62DF\u673A\uFF08\u7B2C3\u7248\uFF09"
          },
          {
            id: 5,
            item: "\u9875\u6570",
            value: "540"
          },
          {
            id: 6,
            item: "\u4E1B\u4E66",
            value: "\u534E\u7AE0\u539F\u521B\u7CBE\u54C1"
          },
          {
            id: 8,
            item: "\u51FA\u7248\u5E74",
            value: "2019-12"
          },
          {
            id: 1,
            item: "\u4F5C\u8005",
            value: "\u5468\u5FD7\u660E"
          }
        ]
      },
      {
        id: 2,
        title: "\u667A\u6167\u7684\u7586\u754C",
        price: 69,
        rate: 9.1,
        description: "<p>\u8FD9\u662F\u4E00\u90E8\u5BF9\u4EBA\u5DE5\u667A\u80FD\u5145\u6EE1\u656C\u754F\u4E4B\u5FC3\u7684\u5320\u5FC3\u4E4B\u4F5C\uFF0C\u7531\u300A\u6DF1\u5165\u7406\u89E3Java\u865A\u62DF\u673A\u300B\u4F5C\u8005\u8017\u65F6\u4E00\u5E74\u5B8C\u6210\uFF0C\u5B83\u5C06\u5E26\u4F60\u4ECE\u5960\u57FA\u4EBA\u7269\u3001\u5386\u53F2\u4E8B\u4EF6\u3001\u5B66\u672F\u7406\u8BBA\u3001\u7814\u7A76\u6210\u679C\u3001\u6280\u672F\u5E94\u7528\u7B495\u4E2A\u7EF4\u5EA6\u5168\u9762\u8BFB\u61C2\u4EBA\u5DE5\u667A\u80FD\u3002</p><p>\u672C\u4E66\u4EE5\u65F6\u95F4\u4E3A\u4E3B\u7EBF\uFF0C\u7528\u4E13\u4E1A\u7684\u77E5\u8BC6\u3001\u901A\u4FD7\u7684\u8BED\u8A00\u3001\u5DE7\u5999\u7684\u5185\u5BB9\u7EC4\u7EC7\u65B9\u5F0F\uFF0C\u8BE6\u7EC6\u8BB2\u89E3\u4E86\u4EBA\u5DE5\u667A\u80FD\u8FD9\u4E2A\u5B66\u79D1\u7684\u5168\u8C8C\u3001\u80FD\u89E3\u51B3\u4EC0\u4E48\u95EE\u9898\u3001\u9762\u4E34\u600E\u6837\u7684\u56F0\u96BE\u3001\u5C1D\u8BD5\u8FC7\u54EA\u4E9B\u52AA\u529B\u3001\u53D6\u5F97\u8FC7\u591A\u5C11\u6210\u7EE9\u3001\u672A\u6765\u5C06\u5411\u4F55\u65B9\u53D1\u5C55\uFF0C\u5C3D\u53EF\u80FD\u6D88\u9664\u4EBA\u5DE5\u667A\u80FD\u7684\u795E\u79D8\u611F\uFF0C\u628A\u9633\u6625\u767D\u96EA\u7684\u4EBA\u5DE5\u667A\u80FD\u4ECE\u79D1\u5B66\u7684\u6BBF\u5802\u63A8\u5411\u516C\u4F17\u9762\u524D\u3002</p>",
        cover: "/static/cover/ai.jpg",
        detail: "/static/desc/ai.jpg",
        specifications: [
          {
            id: 16,
            item: "\u51FA\u7248\u5E74",
            value: "2018-1-1"
          },
          {
            id: 13,
            item: "\u526F\u6807\u9898",
            value: "\u4ECE\u56FE\u7075\u673A\u5230\u4EBA\u5DE5\u667A\u80FD"
          },
          {
            id: 14,
            item: "\u9875\u6570",
            value: "413"
          },
          {
            id: 15,
            item: "\u51FA\u7248\u793E",
            value: "\u673A\u68B0\u5DE5\u4E1A\u51FA\u7248\u793E"
          },
          {
            id: 12,
            item: "\u4E66\u540D",
            value: "\u667A\u6167\u7684\u7586\u754C"
          },
          {
            id: 10,
            item: "\u4F5C\u8005",
            value: "\u5468\u5FD7\u660E"
          },
          {
            id: 17,
            item: "\u88C5\u5E27",
            value: "\u5E73\u88C5"
          },
          {
            id: 11,
            item: "ISBN",
            value: "9787111610496"
          }
        ]
      },
      {
        id: 3,
        title: "Java\u865A\u62DF\u673A\u89C4\u8303\uFF08Java SE 8\uFF09",
        price: 79,
        rate: 7.7,
        description: "<p>\u672C\u4E66\u5B8C\u6574\u800C\u51C6\u786E\u5730\u9610\u91CA\u4E86Java\u865A\u62DF\u673A\u5404\u65B9\u9762\u7684\u7EC6\u8282\uFF0C\u56F4\u7ED5Java\u865A\u62DF\u673A\u6574\u4F53\u67B6\u6784\u3001\u7F16\u8BD1\u5668\u3001class\u6587\u4EF6\u683C\u5F0F\u3001\u52A0\u8F7D\u3001\u94FE\u63A5\u4E0E\u521D\u59CB\u5316\u3001\u6307\u4EE4\u96C6\u7B49\u6838\u5FC3\u4E3B\u9898\u5BF9Java\u865A\u62DF\u673A\u8FDB\u884C\u5168\u9762\u800C\u6DF1\u5165\u7684\u5206\u6790\uFF0C\u6DF1\u523B\u63ED\u793AJava\u865A\u62DF\u673A\u7684\u5DE5\u4F5C\u539F\u7406\u3002\u540C\u65F6\uFF0C\u4E66\u4E2D\u4E0D\u4EC5\u5B8C\u6574\u5730\u8BB2\u8FF0\u4E86\u7531Java SE 8\u6240\u5F15\u5165\u7684\u65B0\u7279\u6027\uFF0C\u4F8B\u5982\u5BF9\u5305\u542B\u9ED8\u8BA4\u5B9E\u73B0\u4EE3\u7801\u7684\u63A5\u53E3\u65B9\u6CD5\u6240\u505A\u7684\u8C03\u7528\uFF0C\u8FD8\u8BB2\u8FF0\u4E86\u4E3A\u652F\u6301\u7C7B\u578B\u6CE8\u89E3\u53CA\u65B9\u6CD5\u53C2\u6570\u6CE8\u89E3\u800C\u5BF9class\u6587\u4EF6\u683C\u5F0F\u6240\u505A\u7684\u6269\u5C55\uFF0C\u5E76\u9610\u660E\u4E86class\u6587\u4EF6\u4E2D\u5404\u5C5E\u6027\u7684\u542B\u4E49\uFF0C\u4EE5\u53CA\u5B57\u8282\u7801\u9A8C\u8BC1\u7684\u89C4\u5219\u3002</p>",
        cover: "/static/cover/jvms8.jpg",
        detail: "",
        specifications: [
          {
            id: 18,
            item: "\u4F5C\u8005",
            value: "Tim Lindholm / Frank Yellin \u7B49"
          },
          {
            id: 24,
            item: "\u51FA\u7248\u793E",
            value: "\u673A\u68B0\u5DE5\u4E1A\u51FA\u7248\u793E"
          },
          {
            id: 25,
            item: "\u51FA\u7248\u5E74",
            value: "2015-6"
          },
          {
            id: 26,
            item: "\u88C5\u5E27",
            value: "\u5E73\u88C5"
          },
          {
            id: 23,
            item: "\u9875\u6570",
            value: "330"
          },
          {
            id: 21,
            item: "\u4E1B\u4E66",
            value: "Java\u6838\u5FC3\u6280\u672F\u7CFB\u5217"
          },
          {
            id: 20,
            item: "\u539F\u4F5C\u540D",
            value: "The Java Virtual Machine Specification, Java SE 8 Edition"
          },
          {
            id: 19,
            item: "\u8BD1\u8005",
            value: "\u7231\u98DE\u7FD4 / \u5468\u5FD7\u660E / \u7B49 "
          },
          {
            id: 22,
            item: "ISBN",
            value: "9787111501596"
          }
        ]
      },
      {
        id: 4,
        title: "\u6DF1\u5165\u7406\u89E3Java\u865A\u62DF\u673A\uFF08\u7B2C2\u7248\uFF09",
        price: 79,
        rate: 9,
        description: "<p>\u300A\u6DF1\u5165\u7406\u89E3Java\u865A\u62DF\u673A:JVM\u9AD8\u7EA7\u7279\u6027\u4E0E\u6700\u4F73\u5B9E\u8DF5(\u7B2C2\u7248)\u300B\u5185\u5BB9\u7B80\u4ECB\uFF1A\u7B2C1\u7248\u4E24\u5E74\u5185\u5370\u5237\u8FD110\u6B21\uFF0C4\u5BB6\u7F51\u4E0A\u4E66\u5E97\u7684\u8BC4\u8BBA\u8FD14?000\u6761\uFF0C98%\u4EE5\u4E0A\u7684\u8BC4\u8BBA\u5168\u90E8\u4E3A5\u661F\u7EA7\u7684\u597D\u8BC4\uFF0C\u662F\u6574\u4E2AJava\u56FE\u4E66\u9886\u57DF\u516C\u8BA4\u7684\u7ECF\u5178\u8457\u4F5C\u548C\u8D85\u7EA7\u7545\u9500\u4E66\uFF0C\u7E41\u4F53\u7248\u5728\u53F0\u6E7E\u4E5F\u5341\u5206\u53D7\u6B22\u8FCE\u3002\u7B2C2\u7248\u5728\u7B2C1\u7248\u7684\u57FA\u7840\u4E0A\u505A\u4E86\u5F88\u5927\u7684\u6539\u8FDB\uFF1A\u6839\u636E\u6700\u65B0\u7684JDK 1.7\u5BF9\u5168\u4E66\u5185\u5BB9\u8FDB\u884C\u4E86\u5168\u9762\u7684\u5347\u7EA7\u548C\u8865\u5145\uFF1B\u589E\u52A0\u4E86\u5927\u91CF\u5904\u7406\u5404\u79CD\u5E38\u89C1JVM\u95EE\u9898\u7684\u6280\u5DE7\u548C\u6700\u4F73\u5B9E\u8DF5\uFF1B\u589E\u52A0\u4E86\u82E5\u5E72\u4E0E\u751F\u4EA7\u73AF\u5883\u76F8\u7ED3\u5408\u7684\u5B9E\u6218\u6848\u4F8B\uFF1B\u5BF9\u7B2C1\u7248\u4E2D\u7684\u9519\u8BEF\u548C\u4E0D\u8DB3\u4E4B\u5904\u7684\u4FEE\u6B63\uFF1B\u7B49\u7B49\u3002\u7B2C2\u7248\u4E0D\u4EC5\u6280\u672F\u66F4\u65B0\u3001\u5185\u5BB9\u66F4\u4E30\u5BCC\uFF0C\u800C\u4E14\u5B9E\u6218\u6027\u66F4\u5F3A\u3002</p><p>\u300A\u6DF1\u5165\u7406\u89E3Java\u865A\u62DF\u673A:JVM\u9AD8\u7EA7\u7279\u6027\u4E0E\u6700\u4F73\u5B9E\u8DF5(\u7B2C2\u7248)\u300B\u5171\u5206\u4E3A\u4E94\u5927\u90E8\u5206\uFF0C\u56F4\u7ED5\u5185\u5B58\u7BA1\u7406\u3001\u6267\u884C\u5B50\u7CFB\u7EDF\u3001\u7A0B\u5E8F\u7F16\u8BD1\u4E0E\u4F18\u5316\u3001\u9AD8\u6548\u5E76\u53D1\u7B49\u6838\u5FC3\u4E3B\u9898\u5BF9JVM\u8FDB\u884C\u4E86\u5168\u9762\u800C\u6DF1\u5165\u7684\u5206\u6790\uFF0C\u6DF1\u523B\u63ED\u793A\u4E86JVM\u7684\u5DE5\u4F5C\u539F\u7406\u3002</p><p>\u7B2C\u4E00\u90E8\u5206\u4ECE\u5B8F\u89C2\u7684\u89D2\u5EA6\u4ECB\u7ECD\u4E86\u6574\u4E2AJava\u6280\u672F\u4F53\u7CFB\u3001Java\u548CJVM\u7684\u53D1\u5C55\u5386\u7A0B\u3001\u6A21\u5757\u5316\uFF0C\u4EE5\u53CAJDK\u7684\u7F16\u8BD1\uFF0C\u8FD9\u5BF9\u7406\u89E3\u4E66\u4E2D\u540E\u9762\u5185\u5BB9\u6709\u91CD\u8981\u5E2E\u52A9\u3002</p><p>\u7B2C\u4E8C\u90E8\u5206\u8BB2\u89E3\u4E86JVM\u7684\u81EA\u52A8\u5185\u5B58\u7BA1\u7406\uFF0C\u5305\u62EC\u865A\u62DF\u673A\u5185\u5B58\u533A\u57DF\u7684\u5212\u5206\u539F\u7406\u4EE5\u53CA\u5404\u79CD\u5185\u5B58\u6EA2\u51FA\u5F02\u5E38\u4EA7\u751F\u7684\u539F\u56E0\uFF1B\u5E38\u89C1\u7684\u5783\u573E\u6536\u96C6\u7B97\u6CD5\u4EE5\u53CA\u5783\u573E\u6536\u96C6\u5668\u7684\u7279\u70B9\u548C\u5DE5\u4F5C\u539F\u7406\uFF1B\u5E38\u89C1\u865A\u62DF\u673A\u76D1\u63A7\u4E0E\u6545\u969C\u5904\u7406\u5DE5\u5177\u7684\u539F\u7406\u548C\u4F7F\u7528\u65B9\u6CD5\u3002</p><p>\u7B2C\u4E09\u90E8\u5206\u5206\u6790\u4E86\u865A\u62DF\u673A\u7684\u6267\u884C\u5B50\u7CFB\u7EDF\uFF0C\u5305\u62EC\u7C7B\u6587\u4EF6\u7ED3\u6784\u3001\u865A\u62DF\u673A\u7C7B\u52A0\u8F7D\u673A\u5236\u3001\u865A\u62DF\u673A\u5B57\u8282\u7801\u6267\u884C\u5F15\u64CE\u3002</p><p>\u7B2C\u56DB\u90E8\u5206\u8BB2\u89E3\u4E86\u7A0B\u5E8F\u7684\u7F16\u8BD1\u4E0E\u4EE3\u7801\u7684\u4F18\u5316\uFF0C\u9610\u8FF0\u4E86\u6CDB\u578B\u3001\u81EA\u52A8\u88C5\u7BB1\u62C6\u7BB1\u3001\u6761\u4EF6\u7F16\u8BD1\u7B49\u8BED\u6CD5\u7CD6\u7684\u539F\u7406\uFF1B\u8BB2\u89E3\u4E86\u865A\u62DF\u673A\u7684\u70ED\u70B9\u63A2\u6D4B\u65B9\u6CD5\u3001HotSpot\u7684\u5373\u65F6\u7F16\u8BD1\u5668\u3001\u7F16\u8BD1\u89E6\u53D1\u6761\u4EF6\uFF0C\u4EE5\u53CA\u5982\u4F55\u4ECE\u865A\u62DF\u673A\u5916\u90E8\u89C2\u5BDF\u548C\u5206\u6790JIT\u7F16\u8BD1\u7684\u6570\u636E\u548C\u7ED3\u679C\uFF1B</p><p>\u7B2C\u4E94\u90E8\u5206\u63A2\u8BA8\u4E86Java\u5B9E\u73B0\u9AD8\u6548\u5E76\u53D1\u7684\u539F\u7406\uFF0C\u5305\u62ECJVM\u5185\u5B58\u6A21\u578B\u7684\u7ED3\u6784\u548C\u64CD\u4F5C\uFF1B\u539F\u5B50\u6027\u3001\u53EF\u89C1\u6027\u548C\u6709\u5E8F\u6027\u5728Java\u5185\u5B58\u6A21\u578B\u4E2D\u7684\u4F53\u73B0\uFF1B\u5148\u884C\u53D1\u751F\u539F\u5219\u7684\u89C4\u5219\u548C\u4F7F\u7528\uFF1B\u7EBF\u7A0B\u5728Java\u8BED\u8A00\u4E2D\u7684\u5B9E\u73B0\u539F\u7406\uFF1B\u865A\u62DF\u673A\u5B9E\u73B0\u9AD8\u6548\u5E76\u53D1\u6240\u505A\u7684\u4E00\u7CFB\u5217\u9501\u4F18\u5316\u63AA\u65BD\u3002</p>",
        cover: "/static/cover/jvm2.jpg",
        detail: "/static/desc/jvm2.jpg",
        specifications: [
          {
            id: 31,
            item: "\u9875\u6570",
            value: "433"
          },
          {
            id: 32,
            item: "\u4E1B\u4E66",
            value: "\u534E\u7AE0\u539F\u521B\u7CBE\u54C1"
          },
          {
            id: 28,
            item: "\u526F\u6807\u9898",
            value: "JVM\u9AD8\u7EA7\u7279\u6027\u4E0E\u6700\u4F73\u5B9E\u8DF5"
          },
          {
            id: 29,
            item: "ISBN",
            value: "9787111421900"
          },
          {
            id: 34,
            item: "\u51FA\u7248\u5E74",
            value: "2013-9-1"
          },
          {
            id: 35,
            item: "\u88C5\u5E27",
            value: "\u5E73\u88C5"
          },
          {
            id: 27,
            item: "\u4F5C\u8005",
            value: "\u5468\u5FD7\u660E"
          },
          {
            id: 30,
            item: "\u4E66\u540D",
            value: "\u6DF1\u5165\u7406\u89E3Java\u865A\u62DF\u673A\uFF08\u7B2C2\u7248\uFF09"
          },
          {
            id: 33,
            item: "\u51FA\u7248\u793E",
            value: "\u673A\u68B0\u5DE5\u4E1A\u51FA\u7248\u793E"
          }
        ]
      },
      {
        id: 5,
        title: "Java\u865A\u62DF\u673A\u89C4\u8303\uFF08Java SE 7\uFF09",
        price: 69,
        rate: 8.9,
        description: "<p>\u672C\u4E66\u6574\u5408\u4E86\u81EA1999\u5E74\u300AJava\u865A\u62DF\u673A\u89C4\u8303\uFF08\u7B2C2\u7248\uFF09\u300B\u53D1\u5E03\u4EE5\u6765Java\u4E16\u754C\u6240\u51FA\u73B0\u7684\u6280\u672F\u53D8\u5316\u3002\u53E6\u5916\uFF0C\u8FD8\u4FEE\u6B63\u4E86\u7B2C2\u7248\u4E2D\u7684\u8BB8\u591A\u9519\u8BEF\uFF0C\u4EE5\u53CA\u5BF9\u76EE\u524D\u4E3B\u6D41Java\u865A\u62DF\u673A\u5B9E\u73B0\u6765\u8BF4\u5DF2\u7ECF\u8FC7\u65F6\u7684\u5185\u5BB9\u3002\u6700\u540E\u8FD8\u5904\u7406\u4E86\u4E00\u4E9BJava\u865A\u62DF\u673A\u548CJava\u8BED\u8A00\u6982\u5FF5\u7684\u6A21\u7CCA\u4E4B\u5904\u3002</p><p>2004\u5E74\u53D1\u5E03\u7684Java SE 5.0\u7248\u4E3AJava\u8BED\u8A00\u5E26\u6765\u4E86\u7FFB\u5929\u8986\u5730\u7684\u53D8\u5316\uFF0C\u4F46\u662F\u5BF9Java\u865A\u62DF\u673A\u8BBE\u8BA1\u7684\u5F71\u54CD\u5219\u76F8\u5BF9\u8F83\u5C0F\u3002\u5728Java SE 7\u8FD9\u4E2A\u7248\u672C\u4E2D\uFF0C\u6211\u4EEC\u6269\u5145\u4E86class\u6587\u4EF6\u683C\u5F0F\u4EE5\u4FBF\u652F\u6301\u65B0\u7684Java\u8BED\u8A00\u7279\u6027\uFF0C\u8B6C\u5982\u6CDB\u578B\u548C\u53D8\u957F\u53C2\u6570\u65B9\u6CD5\u7B49\u3002</p>",
        cover: "/static/cover/jvms.jpg",
        detail: "/static/desc/jvms.jpg",
        specifications: [
          {
            id: 41,
            item: "\u9875\u6570",
            value: "316"
          },
          {
            id: 42,
            item: "\u51FA\u7248\u793E",
            value: "\u673A\u68B0\u5DE5\u4E1A\u51FA\u7248\u793E"
          },
          {
            id: 36,
            item: "\u4F5C\u8005",
            value: "Tim Lindholm / Frank Yellin \u7B49"
          },
          {
            id: 37,
            item: "\u8BD1\u8005",
            value: "\u5468\u5FD7\u660E / \u859B\u7B1B / \u5434\u749E\u6E0A / \u51B6\u79C0\u521A"
          },
          {
            id: 39,
            item: "\u526F\u6807\u9898",
            value: "\u4ECE\u56FE\u7075\u673A\u5230\u4EBA\u5DE5\u667A\u80FD"
          },
          {
            id: 45,
            item: "\u88C5\u5E27",
            value: "\u5E73\u88C5"
          },
          {
            id: 44,
            item: "\u51FA\u7248\u5E74",
            value: "2014-1"
          },
          {
            id: 38,
            item: "\u539F\u4F5C\u540D",
            value: "The Java Virtual Machine Specification, Java SE 7 Edition"
          },
          {
            id: 43,
            item: "\u4E1B\u4E66",
            value: "Java\u6838\u5FC3\u6280\u672F\u7CFB\u5217"
          },
          {
            id: 40,
            item: "ISBN",
            value: "9787111445159"
          }
        ]
      },
      {
        id: 6,
        title: "\u6DF1\u5165\u7406\u89E3OSGi",
        price: 79,
        rate: 7.7,
        description: "<p>\u672C\u4E66\u662F\u539F\u521BJava\u6280\u672F\u56FE\u4E66\u9886\u57DF\u7EE7\u300A\u6DF1\u5165\u7406\u89E3Java\u865A\u62DF\u673A\u300B\u540E\u7684\u53C8\u4E00\u5B9E\u529B\u4E4B\u4F5C\uFF0C\u4E5F\u662F\u5168\u7403\u9996\u672C\u57FA\u4E8E\u6700\u65B0OSGi R5.0\u89C4\u8303\u7684\u8457\u4F5C\u3002\u7406\u8BBA\u65B9\u9762\uFF0C\u65E2\u5168\u9762\u89E3\u8BFB\u4E86OSGi\u89C4\u8303\uFF0C\u6DF1\u523B\u63ED\u793A\u4E86OSGi\u539F\u7406\uFF0C\u8BE6\u7EC6\u8BB2\u89E3\u4E86OSGi\u670D\u52A1\uFF0C\u53C8\u7CFB\u7EDF\u5730\u4ECB\u7ECD\u4E86Equinox\u6846\u67B6\u7684\u4F7F\u7528\u65B9\u6CD5\uFF0C\u5E76\u901A\u8FC7\u6E90\u7801\u5206\u6790\u4E86\u8BE5\u6846\u67B6\u7684\u5DE5\u4F5C\u673A\u5236\uFF1B\u5B9E\u8DF5\u65B9\u9762\uFF0C\u4E0D\u4EC5\u5305\u542B\u4E00\u4E9B\u5178\u578B\u7684\u6848\u4F8B\uFF0C\u8FD8\u603B\u7ED3\u4E86\u5927\u91CF\u7684\u6700\u4F73\u5B9E\u8DF5\uFF0C\u6781\u5177\u5B9E\u8DF5\u6307\u5BFC\u610F\u4E49\u3002</p><p>\u5168\u4E66\u517114\u7AE0\uFF0C\u52064\u4E2A\u90E8\u5206\u3002\u7B2C\u4E00\u90E8\u5206\uFF08\u7B2C1\u7AE0\uFF09\uFF1A\u8D70\u8FD1OSGi\uFF0C\u4E3B\u8981\u4ECB\u7ECD\u4E86\u4EC0\u4E48\u662FOSGi\u4EE5\u53CA\u4E3A\u4EC0\u4E48\u8981\u4F7F\u7528OSGi\u3002\u7B2C\u4E8C\u90E8\u5206\uFF08\u7B2C2\uFF5E4\u7AE0\uFF09\uFF1AOSGi\u89C4\u8303\u4E0E\u539F\u7406\uFF0C\u5BF9\u6700\u65B0\u7684OSGi R5.0\u4E2D\u7684\u6838\u5FC3\u89C4\u8303\u8FDB\u884C\u4E86\u5168\u9762\u7684\u89E3\u8BFB\uFF0C\u9996\u5148\u8BB2\u89E3\u4E86OSGi\u6A21\u5757\u7684\u5EFA\u7ACB\u3001\u63CF\u8FF0\u3001\u4F9D\u8D56\u5173\u7CFB\u7684\u5904\u7406\uFF0C\u7136\u540E\u8BB2\u89E3\u4E86Bundle\u7684\u542F\u52A8\u539F\u7406\u548C\u8C03\u5EA6\u7BA1\u7406\uFF0C\u6700\u540E\u8BB2\u89E3\u4E86\u4E0E\u672C\u5730\u53CA\u8FDC\u7A0B\u670D\u52A1\u76F8\u5173\u7684\u5185\u5BB9\u3002\u7B2C\u4E09\u90E8\u5206\uFF1AOSGi\u670D\u52A1\u4E0EEquinox\u5E94\u7528\u5B9E\u8DF5\uFF08\u7B2C5\uFF5E11\u7AE0\uFF09\uFF0C\u4E0D\u4EC5\u8BE6\u7EC6\u8BB2\u89E3\u4E86OSGi\u670D\u52A1\u7EB2\u8981\u89C4\u8303\u548C\u4F01\u4E1A\u7EA7\u89C4\u8303\u4E2D\u6700\u5E38\u7528\u7684\u51E0\u4E2A\u5B50\u89C4\u8303\u548C\u670D\u52A1\u7684\u6280\u672F\u7EC6\u8282\uFF0C\u8FD8\u901A\u8FC7\u4E00\u4E2A\u57FA\u4E8EEquinox\u7684BBS\u6848\u4F8B\u6F14\u793A\u4E86Equinox\u7684\u4F7F\u7528\u65B9\u6CD5\uFF0C\u6700\u91CD\u8981\u7684\u662F\u8FD8\u901A\u8FC7\u6E90\u7801\u5206\u6790\u4E86Equinox\u5173\u952E\u529F\u80FD\u7684\u5B9E\u73B0\u673A\u5236\u548C\u539F\u7406\u3002\u7B2C\u56DB\u90E8\u5206\uFF1A\u6700\u4F73\u5B9E\u8DF5\uFF08\u7B2C12\uFF5E14\u7AE0\uFF09\uFF0C\u603B\u7ED3\u4E86\u5927\u91CF\u5173\u4E8EOSGi\u7684\u6700\u4F73\u5B9E\u8DF5\uFF0C\u5305\u62EC\u4ECEBundle\u5982\u4F55\u547D\u540D\u3001\u6A21\u5757\u5212\u5206\u3001\u4F9D\u8D56\u5173\u7CFB\u5904\u7406\u5230\u4FDD\u6301OSGi\u52A8\u6001\u6027\u3001\u7BA1\u7406\u7A0B\u5E8F\u542F\u52A8\u987A\u5E8F\u3001\u4F7F\u7528API\u57FA\u7EBF\u7BA1\u7406\u6A21\u5757\u7248\u672C\u7B49\u5404\u65B9\u9762\u7684\u5B9E\u8DF5\u6280\u5DE7\uFF0C\u6B64\u5916\u8FD8\u4ECB\u7ECD\u4E86Spring DM\u7684\u539F\u7406\u4EE5\u53CA\u5982\u4F55\u5728OSGi\u73AF\u8282\u4E2D\u8FDB\u884C\u7A0B\u5E8F\u6D4B\u8BD5\u3002</p>",
        cover: "/static/cover/osgi.jpg",
        detail: "/static/desc/OSGi.jpg",
        specifications: [
          {
            id: 46,
            item: "\u4F5C\u8005",
            value: "\u5468\u5FD7\u660E / \u8C22\u5C0F\u660E "
          },
          {
            id: 49,
            item: "\u4E66\u540D",
            value: "\u667A\u6167\u7684\u7586\u754C"
          },
          {
            id: 50,
            item: "\u4E1B\u4E66",
            value: "\u534E\u7AE0\u539F\u521B\u7CBE\u54C1"
          },
          {
            id: 47,
            item: "\u526F\u6807\u9898",
            value: "Equinox\u539F\u7406\u3001\u5E94\u7528\u4E0E\u6700\u4F73\u5B9E\u8DF5"
          },
          {
            id: 53,
            item: "\u51FA\u7248\u5E74",
            value: "2013-2-25"
          },
          {
            id: 54,
            item: "\u88C5\u5E27",
            value: "\u5E73\u88C5"
          },
          {
            id: 52,
            item: "\u51FA\u7248\u793E",
            value: "\u673A\u68B0\u5DE5\u4E1A\u51FA\u7248\u793E"
          },
          {
            id: 48,
            item: "ISBN",
            value: "9787111408871"
          },
          {
            id: 51,
            item: "\u9875\u6570",
            value: "432"
          }
        ]
      },
      {
        id: 7,
        title: "\u6DF1\u5165\u7406\u89E3Java\u865A\u62DF\u673A",
        price: 69,
        rate: 8.6,
        description: "<p>\u4F5C\u4E3A\u4E00\u4F4DJava\u7A0B\u5E8F\u5458\uFF0C\u4F60\u662F\u5426\u4E5F\u66FE\u7ECF\u60F3\u6DF1\u5165\u7406\u89E3Java\u865A\u62DF\u673A\uFF0C\u4F46\u662F\u5374\u88AB\u5B83\u7684\u590D\u6742\u548C\u6DF1\u5965\u62D2\u4E4B\u95E8\u5916\uFF1F\u6CA1\u5173\u7CFB\uFF0C\u672C\u4E66\u6781\u5C3D\u5316\u7E41\u4E3A\u7B80\u4E4B\u5999\uFF0C\u80FD\u5E26\u9886\u4F60\u5728\u8F7B\u677E\u4E2D\u9886\u7565Java\u865A\u62DF\u673A\u7684\u5965\u79D8\u3002\u672C\u4E66\u662F\u8FD1\u5E74\u6765\u56FD\u5185\u51FA\u7248\u7684\u552F\u4E00\u4E00\u672C\u4E0EJava\u865A\u62DF\u673A\u76F8\u5173\u7684\u4E13\u8457\uFF0C\u4E5F\u662F\u552F\u4E00\u4E00\u672C\u540C\u65F6\u4ECE\u6838\u5FC3\u7406\u8BBA\u548C\u5B9E\u9645\u8FD0\u7528\u8FD9\u4E24\u4E2A\u89D2\u5EA6\u53BB\u63A2\u8BA8Java\u865A\u62DF\u673A\u7684\u8457\u4F5C\uFF0C\u4E0D\u4EC5\u7406\u8BBA\u5206\u6790\u5F97\u900F\u5F7B\uFF0C\u800C\u4E14\u4E66\u4E2D\u5305\u542B\u7684\u5178\u578B\u6848\u4F8B\u548C\u6700\u4F73\u5B9E\u8DF5\u4E5F\u6781\u5177\u73B0\u5B9E\u6307\u5BFC\u610F\u4E49\u3002</p><p>\u5168\u4E66\u5171\u5206\u4E3A\u4E94\u5927\u90E8\u5206\u3002\u7B2C\u4E00\u90E8\u5206\u4ECE\u5B8F\u89C2\u7684\u89D2\u5EA6\u4ECB\u7ECD\u4E86\u6574\u4E2AJava\u6280\u672F\u4F53\u7CFB\u7684\u8FC7\u53BB\u3001\u73B0\u5728\u548C\u672A\u6765\uFF0C\u4EE5\u53CA\u5982\u4F55\u72EC\u7ACB\u5730\u7F16\u8BD1\u4E00\u4E2AOpenJDK7\uFF0C\u8FD9\u5BF9\u7406\u89E3\u540E\u9762\u7684\u5185\u5BB9\u5F88\u6709\u5E2E\u52A9\u3002\u7B2C\u4E8C\u90E8\u5206\u8BB2\u89E3\u4E86JVM\u7684\u81EA\u52A8\u5185\u5B58\u7BA1\u7406\uFF0C\u5305\u62EC\u865A\u62DF\u673A\u5185\u5B58\u533A\u57DF\u7684\u5212\u5206\u539F\u7406\u4EE5\u53CA\u5404\u79CD\u5185\u5B58\u6EA2\u51FA\u5F02\u5E38\u4EA7\u751F\u7684\u539F\u56E0\uFF1B\u5E38\u89C1\u7684\u5783\u573E\u6536\u96C6\u7B97\u6CD5\u4EE5\u53CA\u5783\u573E\u6536\u96C6\u5668\u7684\u7279\u70B9\u548C\u5DE5\u4F5C\u539F\u7406\uFF1B\u5E38\u89C1\u7684\u865A\u62DF\u673A\u7684\u76D1\u63A7\u4E0E\u8C03\u8BD5\u5DE5\u5177\u7684\u539F\u7406\u548C\u4F7F\u7528\u65B9\u6CD5\u3002\u7B2C\u4E09\u90E8\u5206\u5206\u6790\u4E86\u865A\u62DF\u673A\u7684\u6267\u884C\u5B50\u7CFB\u7EDF\uFF0C\u5305\u62ECClass\u7684\u6587\u4EF6\u7ED3\u6784\u4EE5\u53CA\u5982\u4F55\u5B58\u50A8\u548C\u8BBF\u95EEClass\u4E2D\u7684\u6570\u636E\uFF1B\u865A\u62DF\u673A\u7684\u7C7B\u521B\u5EFA\u673A\u5236\u4EE5\u53CA\u7C7B\u52A0\u8F7D\u5668\u7684\u5DE5\u4F5C\u539F\u7406\u548C\u5B83\u5BF9\u865A\u62DF\u673A\u7684\u610F\u4E49\uFF1B\u865A\u62DF\u673A\u5B57\u8282\u7801\u7684\u6267\u884C\u5F15\u64CE\u4EE5\u53CA\u5B83\u5728\u5B9E\u884C\u4EE3\u7801\u65F6\u6D89\u53CA\u7684\u5185\u5B58\u7ED3\u6784\u3002\u7B2C\u56DB\u90E8\u5206\u8BB2\u89E3\u4E86\u7A0B\u5E8F\u7684\u7F16\u8BD1\u4E0E\u4EE3\u7801\u7684\u4F18\u5316\uFF0C\u9610\u8FF0\u4E86\u6CDB\u578B\u3001\u81EA\u52A8\u88C5\u7BB1\u62C6\u7BB1\u3001\u6761\u4EF6\u7F16\u8BD1\u7B49\u8BED\u6CD5\u7CD6\u7684\u539F\u7406\uFF1B\u8BB2\u89E3\u4E86\u865A\u62DF\u673A\u7684\u70ED\u70B9\u63A2\u6D4B\u65B9\u6CD5\u3001HotSpot\u7684\u5373\u65F6\u7F16\u8BD1\u5668\u3001\u7F16\u8BD1\u89E6\u53D1\u6761\u4EF6\uFF0C\u4EE5\u53CA\u5982\u4F55\u4ECE\u865A\u62DF\u673A\u5916\u90E8\u89C2\u5BDF\u548C\u5206\u6790JIT\u7F16\u8BD1\u7684\u6570\u636E\u548C\u7ED3\u679C\u3002\u7B2C\u4E94\u90E8\u5206\u63A2\u8BA8\u4E86Java\u5B9E\u73B0\u9AD8\u6548\u5E76\u53D1\u7684\u539F\u7406\uFF0C\u5305\u62ECJVM\u5185\u5B58\u6A21\u578B\u7684\u7ED3\u6784\u548C\u64CD\u4F5C\uFF1B\u539F\u5B50\u6027\u3001\u53EF\u89C1\u6027\u548C\u6709\u5E8F\u6027\u5728Java\u5185\u5B58\u6A21\u578B\u4E2D\u7684\u4F53\u73B0\uFF1B\u5148\u884C\u53D1\u751F\u539F\u5219\u7684\u89C4\u5219\u548C\u4F7F\u7528\uFF1B\u7EBF\u7A0B\u5728Java\u8BED\u8A00\u4E2D\u7684\u5B9E\u73B0\u539F\u7406\uFF1B\u865A\u62DF\u673A\u5B9E\u73B0\u9AD8\u6548\u5E76\u53D1\u6240\u505A\u7684\u4E00\u7CFB\u5217\u9501\u4F18\u5316\u63AA\u65BD\u3002</p>",
        cover: "/static/cover/jvm1.jpg",
        detail: "",
        specifications: [
          {
            id: 55,
            item: "\u4F5C\u8005",
            value: "\u5468\u5FD7\u660E"
          },
          {
            id: 59,
            item: "\u9875\u6570",
            value: "387"
          },
          {
            id: 61,
            item: "\u51FA\u7248\u5E74",
            value: "2011-6"
          },
          {
            id: 60,
            item: "\u51FA\u7248\u793E",
            value: "\u673A\u68B0\u5DE5\u4E1A\u51FA\u7248\u793E"
          },
          {
            id: 62,
            item: "\u88C5\u5E27",
            value: "\u5E73\u88C5"
          },
          {
            id: 56,
            item: "\u526F\u6807\u9898",
            value: "JVM\u9AD8\u7EA7\u7279\u6027\u4E0E\u6700\u4F73\u5B9E\u8DF5"
          },
          {
            id: 57,
            item: "ISBN",
            value: "9787111349662"
          },
          {
            id: 58,
            item: "\u4E66\u540D",
            value: "\u6DF1\u5165\u7406\u89E3Java\u865A\u62DF\u673A"
          }
        ]
      }
    ];
  }
});

// src/api/mock/json/settlements.json
var require_settlements = __commonJS({
  "src/api/mock/json/settlements.json"(exports, module) {
    module.exports = {
      id: 0,
      createTime: "2020-03-13T16:04:53.388+0000",
      payId: "0904ff25-819b-42d1-9651-dffd79a7893e",
      totalPrice: 141,
      expires: 12e4,
      paymentLink: "https://localhost:8080/pay/modify/0904ff25-819b-42d1-9651-dffd79a7893e?state=PAYED",
      payState: "WAITING"
    };
  }
});

// src/api/mock/json/stockpile.json
var require_stockpile = __commonJS({
  "src/api/mock/json/stockpile.json"(exports, module) {
    module.exports = {
      id: 1,
      product_id: 1,
      amount: 10,
      frozen: 10
    };
  }
});

// require("./json/**/*") in src/api/mock/index.js
var globRequire_json = __glob({
  "./json/accounts.json": () => require_accounts(),
  "./json/advertisements.json": () => require_advertisements(),
  "./json/authorization.json": () => require_authorization(),
  "./json/products.json": () => require_products(),
  "./json/settlements.json": () => require_settlements(),
  "./json/stockpile.json": () => require_stockpile()
});

// src/api/mock/index.js
var MockJS = __require("mockjs");
var loadJSON = (options, file) => {
  const json = globRequire_json("./json/" + file);
  console.debug(`REQUEST\uFF1A${options.type} ${options.url}\uFF1A`, options);
  console.debug("RESPONSE\uFF1A", json);
  return json;
};
var success = (options) => {
  const repo = { code: 0 };
  console.debug(`REQUEST\uFF1A${options.type} ${options.url}\uFF1A`, options);
  console.debug("RESPONSE\uFF1A", repo);
  return repo;
};
var failure = (options) => {
  const repo = { code: 1, message: "\u8FDC\u7A0B\u8C03\u7528\u5DF2\u6B63\u786E\u53D1\u51FA\uFF0C\u4F46\u9759\u6001Mock\u8FD0\u884C\u6A21\u5F0F\u5E76\u6CA1\u6709\u670D\u52A1\u7AEF\u652F\u6301\uFF0C\u6B64\u64CD\u4F5C\u672A\u4EA7\u751F\u6548\u679C" };
  console.debug(`REQUEST\uFF1A${options.type} ${options.url}\uFF1A`, options);
  console.debug("RESPONSE\uFF1A", repo);
  return repo;
};
MockJS.mock("/restful/products", "get", (o) => loadJSON(o, "products.json"));
MockJS.mock("/restful/advertisements", "get", (o) => loadJSON(o, "advertisements.json"));
MockJS.mock("/restful/products", "post", (o) => failure(o));
MockJS.mock("/restful/products", "put", (o) => failure(o));
MockJS.mock(/\/restful\/products\/stockpile\/.*/, "get", (o) => loadJSON(o, "stockpile.json"));
MockJS.mock(/\/restful\/products\/stockpile\/.*/, "patch", (o) => failure(o));
MockJS.mock(/\/restful\/products\/.*/, "get", (o) => {
  let json = loadJSON(o, "products.json");
  let id = /\/restful\/products\/(.*)/.exec(o.url)[1];
  return json.find((book) => id === book.id.toString());
});
MockJS.mock(/\/oauth\/token.*/, "get", (o) => loadJSON(o, "authorization.json"));
MockJS.mock(/\/restful\/accounts\/.*/, "get", (o) => loadJSON(o, "accounts.json"));
MockJS.mock("/restful/accounts", "post", (o) => success(o));
MockJS.mock("/restful/accounts", "put", (o) => success(o));
MockJS.mock("/restful/settlements", "post", (o) => loadJSON(o, "settlements.json"));
MockJS.mock(/\/restful\/pay\/.*/, "patch", (o) => failure(o));
MockJS.mock(/\/restful\/products\/.*/, "delete", (o) => failure(o));
//# sourceMappingURL=data:application/json;base64,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
