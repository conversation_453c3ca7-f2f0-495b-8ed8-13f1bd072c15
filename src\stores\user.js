import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', {
  state: () => ({
    account: {
      username: '',
      name: '',
      email: '',
      telephone: '',
      location: '',
      avatar: ''
    },
    session: {
      username: '',
      token: '',
      expires: null
    },
    favorite: []
  }),

  getters: {
    isAuthorized: (state) => {
      return state.session.token && state.session.expires && new Date() < new Date(state.session.expires)
    },
    isAdministrator: (state) => {
      // 暂时硬编码，实际应该根据用户角色判断
      return state.account.username === 'admin'
    }
  },

  actions: {
    updateAccount(accountData) {
      this.account = { ...this.account, ...accountData }
    },
    
    setSession(sessionData) {
      this.session = { ...sessionData }
    },
    
    clearSession() {
      this.session = {
        username: '',
        token: '',
        expires: null
      }
      this.account = {
        username: '',
        name: '',
        email: '',
        telephone: '',
        location: '',
        avatar: ''
      }
    },
    
    addFavorite(productId) {
      if (!this.favorite.includes(productId)) {
        this.favorite.push(productId)
      }
    },
    
    removeFavorite(productId) {
      const index = this.favorite.indexOf(productId)
      if (index > -1) {
        this.favorite.splice(index, 1)
      }
    },
    
    async refreshSessionTrigger() {
      // 暂时空实现，实际应该设置定时器刷新token
      console.log('refreshSessionTrigger - 暂未实现')
    }
  }
})
