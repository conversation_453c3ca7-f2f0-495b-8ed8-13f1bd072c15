import { defineStore } from 'pinia'

export const useCartStore = defineStore('cart', {
  state: () => ({
    items: [],
    settlement: {
      items: [],
      totalPrice: 0,
      deliveryAddress: '',
      paymentMethod: ''
    }
  }),

  getters: {
    totalItems: (state) => {
      return state.items.reduce((total, item) => total + item.amount, 0)
    },
    
    totalPrice: (state) => {
      return state.items.reduce((total, item) => total + (item.price * item.amount), 0)
    }
  },

  actions: {
    addCartItem(product) {
      const existingItem = this.items.find(item => item.id === product.id)
      if (existingItem) {
        existingItem.amount += 1
      } else {
        this.items.push({
          ...product,
          amount: 1
        })
      }
    },
    
    removeCartItem(productId) {
      const index = this.items.findIndex(item => item.id === productId)
      if (index > -1) {
        this.items.splice(index, 1)
      }
    },
    
    updateCartItemAmount(productId, amount) {
      const item = this.items.find(item => item.id === productId)
      if (item) {
        item.amount = amount
        if (amount <= 0) {
          this.removeCartItem(productId)
        }
      }
    },
    
    clearCart() {
      this.items = []
    },
    
    setupSettlementBillWithDefaultValue(data) {
      this.settlement = {
        ...this.settlement,
        ...data
      }
    }
  }
})
