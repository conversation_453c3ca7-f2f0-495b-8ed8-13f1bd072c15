<template>
  <el-carousel :interval="5000" type="card" height="400px">
    <el-carousel-item v-for="item in advertisements" :key="item.id">
      <el-image :src="item.image" class="image" @click="loadDetail(item.productId)"/>
    </el-carousel-item>
  </el-carousel>
</template>

<script>
// import api from '@/api' // 暂时注释

export default {
  name: 'Carousel',
  data () {
    return {
      // 暂时使用硬编码数据
      advertisements: [
        {
          id: 1,
          productId: 1,
          image: '/static/carousel/1.jpg'
        },
        {
          id: 2,
          productId: 2,
          image: '/static/carousel/2.jpg'
        },
        {
          id: 3,
          productId: 3,
          image: '/static/carousel/3.jpg'
        }
      ]
    }
  },
  async created () {
    // 暂时注释API调用
    // this.advertisements = (await api.warehouse.getAdvertisements()).data
  },
  methods: {
    loadDetail (productId) {
      this.$router.push(`/detail/${productId}`)
    }
  }
}
</script>

<style scoped>
  .el-carousel__item h3 {
    color: #475669;
    font-size: 14px;
    opacity: 0.75;
    line-height: 200px;
    margin: 0;
  }

  .image {
    border: 1px solid #ddd;
    border-radius: 15px;
  }
</style>
