import { defineStore } from 'pinia'

export const useNotificationStore = defineStore('notification', {
  state: () => ({
    exception: null,
    messages: []
  }),

  actions: {
    setException(error) {
      this.exception = error
    },
    
    clearException() {
      this.exception = null
    },
    
    addMessage(message) {
      this.messages.push({
        id: Date.now(),
        ...message
      })
    },
    
    removeMessage(messageId) {
      const index = this.messages.findIndex(msg => msg.id === messageId)
      if (index > -1) {
        this.messages.splice(index, 1)
      }
    },
    
    clearMessages() {
      this.messages = []
    }
  }
})
