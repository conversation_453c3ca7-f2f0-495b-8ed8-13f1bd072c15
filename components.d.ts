/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    Cabinet: typeof import('./src/components/home/<USER>/Cabinet.vue')['default']
    Carousel: typeof import('./src/components/home/<USER>/Carousel.vue')['default']
    Checkstand: typeof import('./src/components/home/<USER>/Checkstand.vue')['default']
    Copyright: typeof import('./src/components/home/<USER>')['default']
    LoginForm: typeof import('./src/components/login/LoginForm.vue')['default']
    NavigationBar: typeof import('./src/components/home/<USER>')['default']
    PayStepIndicator: typeof import('./src/components/home/<USER>/PayStepIndicator.vue')['default']
    ProductManage: typeof import('./src/components/home/<USER>/ProductManage.vue')['default']
    RegistrationForm: typeof import('./src/components/login/RegistrationForm.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    StockManage: typeof import('./src/components/home/<USER>/StockManage.vue')['default']
    UserInformation: typeof import('./src/components/home/<USER>')['default']
  }
}
