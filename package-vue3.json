{"name": "bookstore", "version": "2.0.0", "description": "The Fenix Project Client Demo - Vue 3 Version", "author": "icyfenix <<EMAIL>>", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:mock": "vite build --mode mock", "preview": "vite preview", "lint": "eslint --ext .js,.vue src --fix", "type-check": "vue-tsc --noEmit"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.0", "pinia": "^2.1.0", "element-plus": "^2.4.0", "@element-plus/icons-vue": "^2.3.0", "axios": "^1.6.0", "default-passive-events": "^2.0.0", "crypto-js": "^4.2.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "eslint": "^8.55.0", "eslint-plugin-vue": "^9.19.0", "prettier": "^3.1.0", "typescript": "^5.3.0", "vite": "^5.0.0", "vite-plugin-mock": "^3.0.0", "vue-tsc": "^1.8.0", "mockjs": "^1.1.0", "unplugin-auto-import": "^0.17.0", "unplugin-vue-components": "^0.26.0", "sass": "^1.69.0"}, "engines": {"node": ">= 18.0.0", "npm": ">= 9.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}