import { createApp } from 'vue'
import 'default-passive-events'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import App from './App.vue'
import { createPinia } from 'pinia'
import router from './router'

/**
 * 默认在开发模式中启用mock.js代替服务端请求
 * 如需要同时调试服务端，请修改此处判断条件
 */
if (import.meta.env.VITE_USE_MOCK === 'true' || import.meta.env.MODE === 'mock') {
  import('./api/mock')
}

const app = createApp(App)
const pinia = createPinia()

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(ElementPlus)
app.use(pinia)
app.use(router)

/**
 * 全局异常处理，将所有没有捕获的异常统一显示出来
 */
app.config.errorHandler = (error, instance, info) => {
  console.error('Global error:', error, info)
  // 这里需要等store迁移到Pinia后再处理
  // store.commit('notification/setException', error)
}

app.mount('#app')
