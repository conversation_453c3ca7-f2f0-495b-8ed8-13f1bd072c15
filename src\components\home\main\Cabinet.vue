<template>
  <el-card class="box-card">
    <div slot="header" class="header">
      <span>热销书籍</span>
    </div>
    <el-row :gutter="0">
      <el-col :span="6" v-for="book in books" :key="book.id" class="book-container">
        <el-image :src="book.cover" class="image" @click="loadDetail(book.id)"/>
        <div style="padding: 14px;">
          <span id="price">￥ {{book.price.toFixed(2)}}</span>
          <span id="title">{{book.title}}</span>
          <span id="description">{{pureText(book.description)}}</span>
          <div id="actions">
            <el-button icon="el-icon-money" @click="goDirectSettlement(book)" circle></el-button>
            <el-button :icon="isInCart(book.id) ? 'el-icon-s-goods' : 'el-icon-goods'" circle
                       @click="updateCart(book.id)"></el-button>
            <el-button :icon="isFavorite(book.id) ? 'el-icon-star-on' : 'el-icon-star-off'" circle
                       @click="updateFavorite(book.id)"></el-button>
          </div>
        </div>
      </el-col>
    </el-row>
  </el-card>
</template>

<script>
// import api from '@/api' // 暂时注释
// import {mapState, mapMutations, mapActions} from 'vuex' // 暂时注释

export default {
  name: 'Cabinet',
  data () {
    return {
      // 暂时使用硬编码数据
      books: [
        {
          id: 1,
          title: '深入理解Java虚拟机',
          price: 89.00,
          cover: '/static/cover/1.jpg',
          description: '这是一本深入讲解Java虚拟机的经典书籍...'
        },
        {
          id: 2,
          title: 'Vue.js设计与实现',
          price: 79.00,
          cover: '/static/cover/2.jpg',
          description: '深入Vue.js框架的设计思想和实现原理...'
        },
        {
          id: 3,
          title: 'JavaScript高级程序设计',
          price: 99.00,
          cover: '/static/cover/3.jpg',
          description: 'JavaScript开发者必读的经典教程...'
        },
        {
          id: 4,
          title: 'TypeScript编程',
          price: 69.00,
          cover: '/static/cover/4.jpg',
          description: 'TypeScript从入门到精通...'
        }
      ]
    }
  },
  computed: {
    // 暂时硬编码
    favorite() {
      return []
    },
    items() {
      return []
    }
  },
  async created () {
    // 暂时注释API调用
    // this.books = (await api.warehouse.getAllProducts()).data
  },
  methods: {
    // 暂时注释Vuex相关方法
    // ...mapMutations('user', ['addFavorite', 'removeFavorite']),
    // ...mapMutations('cart', ['addCartItem', 'removeCartItem']),
    // ...mapActions('cart', ['setupSettlementBillWithDefaultValue']),
    /**
     * 判断是否在收藏夹中
     **/
    isFavorite (id) {
      return this.favorite.includes(id)
    },
    /**
     * 判断是否在购物车中
     **/
    isInCart (id) {
      return this.items.find(item => item.id === id)
    },
    /**
     *快捷添加收藏夹，点一下加入，再点移除
     **/
    updateFavorite (id) {
      // 暂时空实现
      this.$notify({
        title: '提示',
        message: '收藏功能暂未实现',
        type: 'info'
      })
    },
    /**
     * 快捷添加购物车，点一下加入，再点移除（哪怕有多件）
     */
    updateCart (id) {
      // 暂时空实现
      this.$notify({
        title: '提示',
        message: '购物车功能暂未实现',
        type: 'info'
      })
    },
    /**
     * 转到商品详情页面
     */
    loadDetail (id) {
      this.$router.push(`/detail/${id}`)
    },
    /**
     * 去除HTML标签
     */
    pureText (text) {
      // 暂时简单实现
      return text ? text.replace(/<[^>]*>/g, '') : ''
    },
    /**
     * 直接支付购买
     */
    goDirectSettlement (product) {
      // 暂时空实现
      this.$notify({
        title: '提示',
        message: '直接购买功能暂未实现',
        type: 'info'
      })
    }
  }
}
</script>

<style scoped>
  .image {
    width: 300px;
    height: 300px;
  }

  #price {
    font-family: Arial, serif;
    font-size: 18px;
    font-weight: bolder;
    color: #d44d44;
    display: block;
  }

  #title {
    font-size: 14px;
    font-weight: 700;
    line-height: 1.2;
    margin: 0 8px;
    color: #333;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    padding: 5px 0 10px 0;
    display: block;
  }

  #description {
    font-size: 12px;
    color: #999;
    text-align: left;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
  }

  #actions {
    padding: 10px 10px 0 0;
  }

  .book-container {
    padding: 20px 0;
    border: 1px solid #fff;
    transition: .2s;
  }

  .book-container:hover {
    border: 1px solid #ddd;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
    cursor: pointer;
  }

</style>
