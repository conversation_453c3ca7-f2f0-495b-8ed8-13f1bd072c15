<template>
  <el-popover placement="top" width="250" v-model="visible" trigger="click">
    <div class="container">
      <a href="http://cn.gravatar.com/" target="_blank">
        <el-avatar :size="64" fit="fill" :src="account.avatar"></el-avatar>
      </a>
      <span style="display: block">{{account.name}}</span>
      <el-form ref="account_form" :model="account" :rules="rules" size="mini" class="account_form">
        <el-form-item size="mini" prop="email">
          <el-input v-model="account.email">
            <template slot="prepend"><i class="el-icon-receiving"></i></template>
          </el-input>
        </el-form-item>
        <el-form-item size="mini" prop="telephone">
          <el-input v-model="account.telephone">
            <template slot="prepend"><i class="el-icon-phone-outline"></i></template>
          </el-input>
        </el-form-item>
        <el-form-item size="mini" prop="location">
          <el-input v-model="account.location">
            <template slot="prepend"><i class="el-icon-map-location"></i></template>
          </el-input>
        </el-form-item>
      </el-form>
      <div style="text-align: center; margin: 5px 0 5px 0">
        <el-button size="mini" type="primary" plain @click="modifyAccount">更新信息</el-button>
        <el-button size="mini" type="danger" plain @click="exitLogin">退出登录</el-button>
      </div>
    </div>
    <el-button :icon="isAuthorized ? 'el-icon-user-solid' : 'el-icon-user'" slot="reference" circle
               @click="changeUserStatue"></el-button>
  </el-popover>
</template>

<script>
// import api from '@/api' // 暂时注释
// import {mapState, mapGetters, mapMutations, mapActions} from 'vuex' // 暂时注释

export default {
  name: 'UserInformation',
  data () {
    return {
      trigger: 'manual',
      visible: false,
      rules: {
        email: [
          {required: true, message: '请填写邮箱', trigger: 'blur'},
          {type: 'email', message: '不符合邮箱格式', trigger: 'blur'}
        ],
        telephone: [
          {required: true, message: '请填写手机', trigger: 'blur'}
        ],
        location: [
          {required: true, message: '请填写地址', trigger: 'blur'}
        ]
      }
    }
  },
  created () {
    if (this.isAuthorized) {
      // Session是具有有效期的，设置更新令牌的触发器
      this.refreshSessionTrigger()
      // Session中有用户，而账号中没有，说明是通过“保存当前登陆状态”得到的，从服务端获取一下用户信息
      if (!this.account.username) {
        this.refreshAccount()
      }
    }
  },
  computed: {
    // 暂时硬编码，等Pinia store创建后再修复
    isAuthorized() {
      return false
    },
    account() {
      return {
        name: '演示用户',
        email: '<EMAIL>',
        telephone: '***********',
        location: '演示地址',
        avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
      }
    },
    session() {
      return {}
    }
  },
  methods: {
    // 暂时注释Vuex相关方法
    // ...mapMutations('user', ['updateAccount', 'clearSession']),
    // ...mapActions('user', ['refreshSessionTrigger']),
    /**
     * 检查用户状态
     * 没有登陆的话，转向登陆页面
     */
    changeUserStatue () {
      if (!this.isAuthorized) {
        this.$router.push('/login')
      }
    },
    /**
     * 从服务端请求用户信息，更新到vuex中
     */
    async refreshAccount () {
      // 暂时空实现，等Pinia store创建后再实现
      console.log('refreshAccount - 暂未实现')
    },

    /**
     * 退出登陆
     */
    exitLogin () {
      // 暂时空实现，等Pinia store创建后再实现
      this.visible = false
      console.log('exitLogin - 暂未实现')
    },

    /**
     * 更新账户信息
     */
    modifyAccount () {
      this.$refs['account_form'].validate(valid => valid ? this.submitModification() : false)
    },
    /**
     * 向服务端提交账户更新
     */
    async submitModification () {
      // 暂时空实现，等API适配后再实现
      console.log('submitModification - 暂未实现')
      this.$notify({title: '提示', message: '功能暂未实现', type: 'info'})
    }
  }
}
</script>

<style scoped>
  .container {
    display: block;
    text-align: center;
  }

  .account_form {
    padding-top: 15px;
  }
</style>
