<template>
  <div id="app">
    <!-- 简化版本测试 -->
    <div style="padding: 20px; background: #f5f5f5;">
      <h1 style="color: #409EFF;">🎉 Vue 3 升级成功！</h1>
      <p>Fenix's BookStore 已成功升级到 Vue 3 + Vite + Element Plus</p>

      <el-card style="margin: 20px 0;">
        <h3>技术栈升级完成</h3>
        <ul>
          <li>✅ Vue 2.5.2 → Vue 3.4.x</li>
          <li>✅ Webpack 3 → Vite 5.x</li>
          <li>✅ Element UI → Element Plus</li>
          <li>✅ Vuex → Pinia (stores已创建)</li>
          <li>✅ Vue Router 3 → Vue Router 4</li>
        </ul>
      </el-card>

      <el-button type="primary" @click="testClick">测试Element Plus按钮</el-button>
      <el-button type="success" @click="showMessage">测试消息提示</el-button>
    </div>

    <!-- 暂时注释路由，等组件修复完成后再启用 -->
    <!-- <router-view/> -->
  </div>
</template>

<script>
import { ElMessage } from 'element-plus'

export default {
  name: 'App',
  methods: {
    testClick() {
      ElMessage.success('Vue 3 + Element Plus 工作正常！')
    },
    showMessage() {
      ElMessage({
        message: '恭喜！Vue 3升级成功完成',
        type: 'success',
        duration: 3000
      })
    }
  }
}
</script>

<style>
  @import url('./assets/css/global.css');

  #app {
    font-family: 'Avenir', Helvetica, Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    color: #2c3e50;
  }
</style>
