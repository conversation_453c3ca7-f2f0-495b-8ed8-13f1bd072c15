<template>
  <div id="app">
    <!-- 暂时注释异常处理，等Pinia store创建后再启用 -->
    <!-- <transition name="slide-fade">
      <el-alert title="接收到未处理的异常：" type="error" :description="exception.message" show-icon v-if="exception"
                @close="clearMessage">
      </el-alert>
    </transition> -->
    <router-view/>
  </div>
</template>

<script>
export default {
  name: 'App'
}
</script>

<style>
  @import url('./assets/css/global.css');

  #app {
    font-family: 'Avenir', Helvetica, Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    color: #2c3e50;
  }
</style>
